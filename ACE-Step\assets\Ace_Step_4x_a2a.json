{"id": "88ac5dad-efd7-40bb-84fe-fbaefdee1fa9", "revision": 0, "last_node_id": 63, "last_link_id": 173, "nodes": [{"id": 45, "type": "ModelSamplingSD3", "pos": [542.9647827148438, -90.26543426513672], "size": [270, 58], "flags": {}, "order": 3, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 111}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [112, 116, 123, 132]}], "properties": {"Node name for S&R": "ModelSamplingSD3"}, "widgets_values": [4.000000000000001]}, {"id": 18, "type": "VAEDecodeAudio", "pos": [886.7603149414062, -89.60011291503906], "size": [150.93612670898438, 46], "flags": {}, "order": 9, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 101}, {"name": "vae", "type": "VAE", "link": 83}], "outputs": [{"name": "AUDIO", "type": "AUDIO", "links": [26]}], "properties": {"Node name for S&R": "VAEDecodeAudio"}, "widgets_values": []}, {"id": 47, "type": "VAEDecodeAudio", "pos": [876.7603759765625, 180.3998260498047], "size": [150.93612670898438, 46], "flags": {}, "order": 12, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 117}, {"name": "vae", "type": "VAE", "link": 118}], "outputs": [{"name": "AUDIO", "type": "AUDIO", "links": [120]}], "properties": {"Node name for S&R": "VAEDecodeAudio"}, "widgets_values": []}, {"id": 54, "type": "VAEDecodeAudio", "pos": [886.7603149414062, 530.3997192382812], "size": [150.93612670898438, 46], "flags": {}, "order": 15, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 147}, {"name": "vae", "type": "VAE", "link": 128}], "outputs": [{"name": "AUDIO", "type": "AUDIO", "links": [130]}], "properties": {"Node name for S&R": "VAEDecodeAudio"}, "widgets_values": []}, {"id": 40, "type": "CheckpointLoaderSimple", "pos": [83.06548309326172, -53.6221923828125], "size": [375, 98], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [111]}, {"name": "CLIP", "type": "CLIP", "links": [80]}, {"name": "VAE", "type": "VAE", "links": [83, 118, 128, 137, 149]}], "properties": {"Node name for S&R": "CheckpointLoaderSimple"}, "widgets_values": ["ace_step_v1_3.5b.safetensors"]}, {"id": 57, "type": "VAEDecodeAudio", "pos": [886.7603149414062, 800.3997802734375], "size": [150.93612670898438, 46], "flags": {}, "order": 18, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 136}, {"name": "vae", "type": "VAE", "link": 137}], "outputs": [{"name": "AUDIO", "type": "AUDIO", "links": [168]}], "properties": {"Node name for S&R": "VAEDecodeAudio"}, "widgets_values": []}, {"id": 62, "type": "Reroute", "pos": [80, 60], "size": [75, 26], "flags": {}, "order": 7, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 173}], "outputs": [{"name": "", "type": "LATENT", "links": [172]}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 60, "type": "VAEEncodeAudio", "pos": [-122.92820739746094, 7.071800231933594], "size": [150.13333129882812, 46], "flags": {}, "order": 5, "mode": 0, "inputs": [{"name": "audio", "type": "AUDIO", "link": 150}, {"name": "vae", "type": "VAE", "link": 149}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [173]}], "properties": {"Node name for S&R": "VAEEncodeAudio"}, "color": "#223", "bgcolor": "#335"}, {"id": 17, "type": "EmptyAceStepLatentAudio", "pos": [-245.60769653320312, 112.92820739746094], "size": [270, 82], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "LATENT", "type": "LATENT", "links": []}], "properties": {"Node name for S&R": "EmptyAceStepLatentAudio"}, "widgets_values": [69, 1], "color": "#223", "bgcolor": "#335"}, {"id": 44, "type": "ConditioningZeroOut", "pos": [291.3594665527344, 486.2049865722656], "size": [197.712890625, 26], "flags": {}, "order": 6, "mode": 0, "inputs": [{"name": "conditioning", "type": "CONDITIONING", "link": 108}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [109, 114, 125, 134]}], "properties": {"Node name for S&R": "ConditioningZeroOut"}, "widgets_values": []}, {"id": 46, "type": "K<PERSON><PERSON><PERSON>", "pos": [526.7609252929688, 330.3997802734375], "size": [315, 262], "flags": {}, "order": 10, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 116}, {"name": "positive", "type": "CONDITIONING", "link": 115}, {"name": "negative", "type": "CONDITIONING", "link": 114}, {"name": "latent_image", "type": "LATENT", "link": 169}], "outputs": [{"name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [117, 170]}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [701612340335405, "increment", 30, 6, "euler", "sgm_uniform", 0.20000000000000004]}, {"id": 53, "type": "K<PERSON><PERSON><PERSON>", "pos": [526.7609252929688, 640.3997802734375], "size": [315, 262], "flags": {}, "order": 13, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 123}, {"name": "positive", "type": "CONDITIONING", "link": 124}, {"name": "negative", "type": "CONDITIONING", "link": 125}, {"name": "latent_image", "type": "LATENT", "link": 170}], "outputs": [{"name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [147, 171]}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [801612340336968, "increment", 30, 6, "euler", "sgm_uniform", 0.20000000000000004]}, {"id": 56, "type": "K<PERSON><PERSON><PERSON>", "pos": [526.7609252929688, 960.3997802734375], "size": [315, 262], "flags": {}, "order": 16, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 132}, {"name": "positive", "type": "CONDITIONING", "link": 133}, {"name": "negative", "type": "CONDITIONING", "link": 134}, {"name": "latent_image", "type": "LATENT", "link": 171}], "outputs": [{"name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [136]}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [205612340336968, "increment", 30, 6, "euler", "sgm_uniform", 0.20000000000000004]}, {"id": 3, "type": "K<PERSON><PERSON><PERSON>", "pos": [524.7819213867188, 24.392868041992188], "size": [315, 262], "flags": {}, "order": 8, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 112}, {"name": "positive", "type": "CONDITIONING", "link": 110}, {"name": "negative", "type": "CONDITIONING", "link": 109}, {"name": "latent_image", "type": "LATENT", "link": 172}], "outputs": [{"name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [101, 169]}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [654498564630224, "increment", 30, 6, "euler", "sgm_uniform", 0.4000000000000001]}, {"id": 59, "type": "LoadAudio", "pos": [-547.00732421875, 44.58967590332031], "size": [274, 136], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"name": "AUDIO", "type": "AUDIO", "links": [150]}], "properties": {"Node name for S&R": "LoadAudio"}, "widgets_values": ["The_Gael_Synth.mp3", null, ""], "color": "#432", "bgcolor": "#653"}, {"id": 14, "type": "TextEncodeAceStepAudio", "pos": [81.86527252197266, 132.01605224609375], "size": [410.834716796875, 305.39215087890625], "flags": {}, "order": 4, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 80}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [108, 110, 115, 124, 133]}], "properties": {"Node name for S&R": "TextEncodeAceStepAudio"}, "widgets_values": ["techno, eurodance, melodic, dark synth, dark soundscape", ""], "color": "#232", "bgcolor": "#353"}, {"id": 19, "type": "SaveAudio", "pos": [-662.6826782226562, 248.9967041015625], "size": [725.9335327148438, 112], "flags": {}, "order": 11, "mode": 0, "inputs": [{"name": "audio", "type": "AUDIO", "link": 26}], "outputs": [], "properties": {}, "widgets_values": ["audio/asdf"]}, {"id": 61, "type": "SaveAudio", "pos": [-649.760498046875, 716.68798828125], "size": [705.8839721679688, 112], "flags": {}, "order": 19, "mode": 0, "inputs": [{"name": "audio", "type": "AUDIO", "link": 168}], "outputs": [], "properties": {}, "widgets_values": ["audio/asdf"]}, {"id": 49, "type": "SaveAudio", "pos": [-654.6276245117188, 408.31817626953125], "size": [715.1640014648438, 112], "flags": {}, "order": 14, "mode": 0, "inputs": [{"name": "audio", "type": "AUDIO", "link": 120}], "outputs": [], "properties": {}, "widgets_values": ["audio/asdf"]}, {"id": 55, "type": "SaveAudio", "pos": [-659.52783203125, 558.103515625], "size": [723.4810180664062, 112], "flags": {}, "order": 17, "mode": 0, "inputs": [{"name": "audio", "type": "AUDIO", "link": 130}], "outputs": [], "properties": {}, "widgets_values": ["audio/asdf"]}], "links": [[26, 18, 0, 19, 0, "AUDIO"], [80, 40, 1, 14, 0, "CLIP"], [83, 40, 2, 18, 1, "VAE"], [101, 3, 0, 18, 0, "LATENT"], [108, 14, 0, 44, 0, "CONDITIONING"], [109, 44, 0, 3, 2, "CONDITIONING"], [110, 14, 0, 3, 1, "CONDITIONING"], [111, 40, 0, 45, 0, "MODEL"], [112, 45, 0, 3, 0, "MODEL"], [114, 44, 0, 46, 2, "CONDITIONING"], [115, 14, 0, 46, 1, "CONDITIONING"], [116, 45, 0, 46, 0, "MODEL"], [117, 46, 0, 47, 0, "LATENT"], [118, 40, 2, 47, 1, "VAE"], [120, 47, 0, 49, 0, "AUDIO"], [123, 45, 0, 53, 0, "MODEL"], [124, 14, 0, 53, 1, "CONDITIONING"], [125, 44, 0, 53, 2, "CONDITIONING"], [128, 40, 2, 54, 1, "VAE"], [130, 54, 0, 55, 0, "AUDIO"], [132, 45, 0, 56, 0, "MODEL"], [133, 14, 0, 56, 1, "CONDITIONING"], [134, 44, 0, 56, 2, "CONDITIONING"], [136, 56, 0, 57, 0, "LATENT"], [137, 40, 2, 57, 1, "VAE"], [147, 53, 0, 54, 0, "LATENT"], [149, 40, 2, 60, 1, "VAE"], [150, 59, 0, 60, 0, "AUDIO"], [168, 57, 0, 61, 0, "AUDIO"], [169, 3, 0, 46, 3, "LATENT"], [170, 46, 0, 53, 3, "LATENT"], [171, 53, 0, 56, 3, "LATENT"], [172, 62, 0, 3, 3, "LATENT"], [173, 60, 0, 62, 0, "*"]], "groups": [], "config": {}, "extra": {"frontendVersion": "1.18.9", "VHS_latentpreview": false, "VHS_latentpreviewrate": 0}, "version": 0.4}